<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NButton, NIcon, NProgress, NStatistic } from 'naive-ui';
import DashboardHeightConfig from '@/components/dashboard/containers/dashboard-height-config.vue';

defineOptions({
  name: 'DashboardHome'
});

const isLoaded = ref(false);

// 卡片高度配置
const cardHeights = ref({
  left: {
    top: 40, // 数据概览 40%
    middle: 30, // 系统监控 30%
    bottom: 30 // 实时活动 30%
  },
  right: {
    top: 40, // 关键指标 40%
    middle: 30, // 统计图表 30%
    bottom: 30 // 快速操作 30%
  }
});

// 高度配置对话框显示状态
const heightConfigVisible = ref(false);

// 计算动态样式
const getCardStyle = (side: 'left' | 'right', position: 'top' | 'middle' | 'bottom') => {
  const height = cardHeights.value[side][position];
  return {
    flex: `0 0 ${height}%`,
    height: `${height}%`
  };
};

// 应用高度配置
const applyHeightConfig = (config: typeof cardHeights.value) => {
  cardHeights.value = { ...config };
};

// 显示配置对话框
const showHeightConfig = () => {
  heightConfigVisible.value = true;
};

// 业务数据
const chartData = ref([
  {
    name: '销售额',
    value: 12580,
    unit: '万元',
    icon: 'mdi:currency-usd',
    trend: '+12.5%',
    color: '#52c41a',
    bgColor: 'from-green-500/20 to-green-600/10'
  },
  {
    name: '订单数',
    value: 3456,
    unit: '笔',
    icon: 'mdi:shopping',
    trend: '+8.2%',
    color: '#1890ff',
    bgColor: 'from-blue-500/20 to-blue-600/10'
  },
  {
    name: '用户数',
    value: 8901,
    unit: '人',
    icon: 'mdi:account-group',
    trend: '+15.3%',
    color: '#722ed1',
    bgColor: 'from-purple-500/20 to-purple-600/10'
  },
  {
    name: '转化率',
    value: 23.5,
    unit: '%',
    icon: 'mdi:trending-up',
    trend: '+2.1%',
    color: '#fa8c16',
    bgColor: 'from-orange-500/20 to-orange-600/10'
  }
]);

// 系统监控数据
const systemMetrics = ref([
  { name: 'CPU使用率', value: 45, color: '#52c41a', status: 'normal' },
  { name: '内存使用率', value: 68, color: '#faad14', status: 'warning' },
  { name: '磁盘使用率', value: 32, color: '#52c41a', status: 'normal' },
  { name: '网络带宽', value: 78, color: '#ff4d4f', status: 'error' }
]);

// 实时活动数据
const recentActivities = ref([
  { time: '10:30', event: '用户登录', count: 1247, type: 'user', icon: 'mdi:login' },
  { time: '10:25', event: '订单创建', count: 89, type: 'order', icon: 'mdi:cart-plus' },
  { time: '10:20', event: '支付完成', count: 156, type: 'payment', icon: 'mdi:credit-card-check' },
  { time: '10:15', event: '数据同步', count: 1, type: 'system', icon: 'mdi:sync' },
  { time: '10:10', event: '系统备份', count: 1, type: 'system', icon: 'mdi:backup-restore' }
]);

// 快速操作数据
const quickActions = ref([
  { name: '导出数据', icon: 'mdi:download', color: 'text-blue-400', action: 'export' },
  { name: '打印报表', icon: 'mdi:printer', color: 'text-green-400', action: 'print' },
  { name: '分享链接', icon: 'mdi:share', color: 'text-purple-400', action: 'share' },
  { name: '配置面板', icon: 'mdi:cog', color: 'text-orange-400', action: 'config' }
]);

// 计算属性
const getActivityTypeColor = computed(() => (type: string) => {
  const colors = {
    user: 'text-blue-400',
    order: 'text-green-400',
    payment: 'text-purple-400',
    system: 'text-orange-400'
  };
  return colors[type as keyof typeof colors] || 'text-gray-400';
});

// 处理快速操作
const handleQuickAction = (action: string) => {
  console.log(`执行操作: ${action}`);
  // 这里可以添加具体的操作逻辑
};

// 刷新数据
const refreshData = () => {
  console.log('刷新数据');
  // 这里可以添加数据刷新逻辑
};

// 全屏显示
const handleFullscreen = () => {
  console.log('全屏显示');
  // 这里可以添加全屏逻辑
};

onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);
});
</script>

<template>
  <div
    class="h-full translate-y-20px p-0 opacity-0 transition-all duration-600 ease-out"
    :class="{ 'opacity-100 translate-y-0': isLoaded }"
  >
    <!-- 配置按钮 -->
    <div class="absolute right-20px top-20px z-10">
      <NButton circle type="primary" size="large" class="shadow-lg" @click="showHeightConfig">
        <template #icon>
          <NIcon size="20">
            <SvgIcon icon="mdi:tune" />
          </NIcon>
        </template>
      </NButton>
    </div>

    <!-- 主网格布局 -->
    <div class="dashboard-home-grid grid grid-cols-4 h-full min-h-0 gap-24px overflow-hidden">
      <!-- 左侧列 -->
      <div class="h-full min-h-0 flex flex-col gap-24px">
        <!-- 数据概览卡片 -->
        <DashboardCard
          title="数据概览"
          title-icon="mdi:chart-box-outline"
          card-type="metric"
          :animation-delay="0.1"
          :style="getCardStyle('left', 'top')"
        >
          <div class="h-full flex flex-col gap-16px">
            <div
              v-for="(item, index) in chartData.slice(0, 2)"
              :key="item.name"
              class="flex items-center gap-16px border border-white/10 rounded-12px bg-gradient-to-r p-16px backdrop-blur-10px transition-all duration-300 hover:translate-x-4px hover:border-white/20 hover:shadow-[0_4px_16px_rgba(24,144,255,0.2)]"
              :class="item.bgColor"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <!-- 图标 -->
              <div
                class="h-48px w-48px flex items-center justify-center border border-white/20 rounded-12px bg-white/10"
              >
                <NIcon size="24" :style="{ color: item.color }">
                  <SvgIcon :icon="item.icon" />
                </NIcon>
              </div>

              <!-- 数据信息 -->
              <div class="flex-1">
                <div class="mb-4px text-14px text-white/70 font-500">{{ item.name }}</div>
                <div class="mb-4px text-24px text-white font-700">
                  <NStatistic :value="item.value" :precision="item.name === '转化率' ? 1 : 0">
                    <template #suffix>
                      <span class="text-16px">{{ item.unit }}</span>
                    </template>
                  </NStatistic>
                </div>
                <div class="rounded-6px bg-white/10 px-8px py-2px text-12px font-600" :style="{ color: item.color }">
                  {{ item.trend }}
                </div>
              </div>
            </div>
          </div>
        </DashboardCard>

        <!-- 系统监控卡片 -->
        <DashboardCard
          title="系统监控"
          title-icon="mdi:monitor-dashboard"
          card-type="info"
          :animation-delay="0.2"
          :style="getCardStyle('left', 'middle')"
        >
          <div class="dashboard-scrollbar-thin h-full flex flex-col gap-16px overflow-y-auto">
            <div
              v-for="metric in systemMetrics"
              :key="metric.name"
              class="flex flex-col gap-8px border border-white/10 rounded-8px bg-white/5 p-12px transition-all duration-200 hover:bg-white/8"
            >
              <!-- 指标头部 -->
              <div class="flex items-center justify-between">
                <span class="text-14px text-white/80 font-500">{{ metric.name }}</span>
                <div class="flex items-center gap-6px">
                  <span class="text-14px text-white font-600">{{ metric.value }}%</span>
                  <div
                    class="h-8px w-8px animate-pulse rounded-full"
                    :class="{
                      'bg-green-400': metric.status === 'normal',
                      'bg-yellow-400': metric.status === 'warning',
                      'bg-red-400': metric.status === 'error'
                    }"
                  />
                </div>
              </div>

              <!-- 进度条 -->
              <NProgress
                :percentage="metric.value"
                :color="metric.color"
                :show-indicator="false"
                :height="8"
                :border-radius="4"
                class="progress-bar"
              />
            </div>
          </div>
        </DashboardCard>

        <!-- 实时活动卡片 -->
        <DashboardCard
          title="实时活动"
          title-icon="mdi:pulse"
          card-type="info"
          :animation-delay="0.3"
          :style="getCardStyle('left', 'bottom')"
        >
          <div class="dashboard-scrollbar-thin h-full flex flex-col gap-8px overflow-y-auto">
            <div
              v-for="activity in recentActivities.slice(0, 4)"
              :key="activity.time"
              class="flex items-center gap-12px border border-white/10 rounded-8px bg-white/5 p-12px transition-all duration-200 hover:translate-x-2px hover:bg-white/8"
            >
              <!-- 活动图标 -->
              <div class="h-32px w-32px flex items-center justify-center rounded-8px bg-white/10">
                <NIcon size="16" :class="getActivityTypeColor(activity.type)">
                  <SvgIcon :icon="activity.icon" />
                </NIcon>
              </div>

              <!-- 活动信息 -->
              <div class="min-w-0 flex-1">
                <div class="flex items-center justify-between">
                  <span class="truncate text-13px text-white/80 font-500">{{ activity.event }}</span>
                  <span class="ml-8px text-11px text-white/60 font-500">{{ activity.time }}</span>
                </div>
              </div>

              <!-- 活动计数 -->
              <div
                class="h-24px min-w-40px flex items-center justify-center border border-blue-400/30 rounded-6px bg-blue-500/20 px-8px"
              >
                <span class="text-11px text-blue-300 font-600">{{ activity.count }}</span>
              </div>
            </div>
          </div>
        </DashboardCard>
      </div>

      <!-- 中间列 -->
      <div class="col-span-2 h-full min-h-0 flex flex-col gap-24px">
        <!-- 主要图表卡片 -->
        <DashboardCard
          title="数据趋势"
          title-icon="mdi:chart-line"
          card-type="chart"
          :show-actions="true"
          :animation-delay="0.4"
          class="flex-1"
          @refresh="refreshData"
          @fullscreen="handleFullscreen"
        >
          <div
            class="h-full flex items-center justify-center border border-white/10 rounded-12px from-blue-500/5 to-purple-500/5 bg-gradient-to-br"
          >
            <div class="text-center">
              <div class="mb-16px flex justify-center">
                <div
                  class="h-80px w-80px flex items-center justify-center border border-blue-400/40 rounded-20px bg-blue-500/20 backdrop-blur-10px"
                >
                  <NIcon size="40" class="text-blue-300">
                    <SvgIcon icon="mdi:chart-line" />
                  </NIcon>
                </div>
              </div>
              <h3 class="mb-8px text-18px text-white font-600">主要图表区域</h3>
              <p class="text-14px text-white/60">这里可以放置 ECharts 或其他图表组件</p>
            </div>
          </div>
        </DashboardCard>

        <!-- 数据分析卡片 -->
        <DashboardCard
          title="数据分析"
          title-icon="mdi:chart-bar"
          card-type="chart"
          :animation-delay="0.5"
          class="flex-1"
        >
          <div
            class="h-full flex items-center justify-center border border-white/10 rounded-12px from-green-500/5 to-blue-500/5 bg-gradient-to-br"
          >
            <div class="text-center">
              <div class="mb-16px flex justify-center">
                <div
                  class="h-80px w-80px flex items-center justify-center border border-green-400/40 rounded-20px bg-green-500/20 backdrop-blur-10px"
                >
                  <NIcon size="40" class="text-green-300">
                    <SvgIcon icon="mdi:chart-bar" />
                  </NIcon>
                </div>
              </div>
              <h3 class="mb-8px text-18px text-white font-600">分析图表区域</h3>
              <p class="text-14px text-white/60">这里可以放置数据分析相关的图表</p>
            </div>
          </div>
        </DashboardCard>
      </div>

      <!-- 右侧列 -->
      <div class="h-full min-h-0 flex flex-col gap-24px">
        <!-- 关键指标卡片 -->
        <DashboardCard
          title="关键指标"
          title-icon="mdi:speedometer"
          card-type="metric"
          :animation-delay="0.6"
          :style="getCardStyle('right', 'top')"
        >
          <div class="h-full flex flex-col gap-16px">
            <div
              v-for="(item, index) in chartData.slice(2, 4)"
              :key="item.name"
              class="flex items-center gap-16px border border-white/10 rounded-12px bg-gradient-to-r p-16px backdrop-blur-10px transition-all duration-300 hover:translate-x-4px hover:border-white/20 hover:shadow-[0_4px_16px_rgba(24,144,255,0.2)]"
              :class="item.bgColor"
              :style="{ animationDelay: `${(index + 2) * 0.1}s` }"
            >
              <!-- 图标 -->
              <div
                class="h-48px w-48px flex items-center justify-center border border-white/20 rounded-12px bg-white/10"
              >
                <NIcon size="24" :style="{ color: item.color }">
                  <SvgIcon :icon="item.icon" />
                </NIcon>
              </div>

              <!-- 数据信息 -->
              <div class="flex-1">
                <div class="mb-4px text-14px text-white/70 font-500">{{ item.name }}</div>
                <div class="mb-4px text-24px text-white font-700">
                  <NStatistic :value="item.value" :precision="item.name === '转化率' ? 1 : 0">
                    <template #suffix>
                      <span class="text-16px">{{ item.unit }}</span>
                    </template>
                  </NStatistic>
                </div>
                <div class="rounded-6px bg-white/10 px-8px py-2px text-12px font-600" :style="{ color: item.color }">
                  {{ item.trend }}
                </div>
              </div>
            </div>
          </div>
        </DashboardCard>

        <!-- 统计图表卡片 -->
        <DashboardCard
          title="统计图表"
          title-icon="mdi:chart-pie"
          card-type="chart"
          :animation-delay="0.7"
          :style="getCardStyle('right', 'middle')"
        >
          <div
            class="h-full flex items-center justify-center border border-white/10 rounded-12px from-purple-500/5 to-pink-500/5 bg-gradient-to-br"
          >
            <div class="text-center">
              <div class="mb-16px flex justify-center">
                <div
                  class="h-80px w-80px flex items-center justify-center border border-purple-400/40 rounded-20px bg-purple-500/20 backdrop-blur-10px"
                >
                  <NIcon size="40" class="text-purple-300">
                    <SvgIcon icon="mdi:chart-pie" />
                  </NIcon>
                </div>
              </div>
              <h3 class="mb-8px text-18px text-white font-600">统计图表区域</h3>
              <p class="text-14px text-white/60">这里可以放置饼图、环形图等统计图表</p>
            </div>
          </div>
        </DashboardCard>

        <!-- 快速操作卡片 -->
        <DashboardCard
          title="快速操作"
          title-icon="mdi:lightning-bolt"
          card-type="info"
          :animation-delay="0.8"
          :style="getCardStyle('right', 'bottom')"
        >
          <div class="grid grid-cols-2 h-full gap-12px">
            <div
              v-for="action in quickActions"
              :key="action.action"
              class="flex flex-col cursor-pointer items-center justify-center gap-12px border border-white/10 rounded-12px bg-white/5 p-16px transition-all duration-300 hover:translate-y-[-4px] hover:border-white/20 hover:bg-white/10 hover:shadow-[0_8px_24px_rgba(24,144,255,0.3)]"
              @click="handleQuickAction(action.action)"
            >
              <!-- 操作图标 -->
              <div
                class="h-40px w-40px flex items-center justify-center border border-white/20 rounded-12px bg-white/10"
              >
                <NIcon size="20" :class="action.color">
                  <SvgIcon :icon="action.icon" />
                </NIcon>
              </div>

              <!-- 操作名称 -->
              <span class="text-center text-12px text-white/80 font-500 leading-tight">
                {{ action.name }}
              </span>
            </div>
          </div>
        </DashboardCard>
      </div>
    </div>

    <!-- 高度配置对话框 -->
    <DashboardHeightConfig v-model="cardHeights" v-model:visible="heightConfigVisible" @apply="applyHeightConfig" />
  </div>
</template>

<style scoped>
.dashboard-home-grid {
  /* 默认使用固定高度（缩放模式），减去 header + footer + padding */
  height: calc(1080px - 75px - 28px - 40px);
  max-height: calc(1080px - 75px - 28px - 40px);
}

/* 在非缩放模式下使用视口高度 */
:global(.dashboard-layout.no-scale) .dashboard-home-grid {
  height: calc(100vh - 75px - 28px - 40px);
  max-height: none;
  min-height: 600px;
}

/* 自定义高度的卡片 */
.card-height-40 {
  flex: 0 0 40%;
  height: 40%;
}

.card-height-30 {
  flex: 0 0 30%;
  height: 30%;
}

.card-height-25 {
  flex: 0 0 25%;
  height: 25%;
}

.card-height-35 {
  flex: 0 0 35%;
  height: 35%;
}
</style>
