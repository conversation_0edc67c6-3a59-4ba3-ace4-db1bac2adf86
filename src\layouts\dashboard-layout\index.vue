<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NConfigProvider } from 'naive-ui';
import { dashboardTheme } from '@/theme/dashboard-theme';
import GlobalContent from '../modules/global-content/index.vue';
import DashboardHeader from './modules/dashboard-header.vue';
import DashboardFooter from './modules/dashboard-footer.vue';
import DashboardContain from './modules/dashboard-contain.vue';

defineOptions({
  name: 'DashboardLayout'
});

const isAnimating = ref(true);
const enableScale = ref(true);

// 处理缩放设置变更
const handleScaleChange = (value: boolean) => {
  enableScale.value = value;
};

onMounted(() => {
  // 页面进入动画
  setTimeout(() => {
    isAnimating.value = false;
  }, 100);
});
</script>

<template>
  <NConfigProvider :theme-overrides="dashboardTheme" class="h-full">
    <div class="relative h-full w-full overflow-hidden dashboard-layout" :class="{ 'no-scale': !enableScale }">
      <DashboardContain :enable-scale="enableScale">
        <!-- 顶部标题栏和导航栏 -->
        <DashboardHeader title="智能数据大屏" @update:scale="handleScaleChange" />

        <!-- 内容区域 -->
        <div
          class="relative z-1000 flex-1 min-h-0 overflow-hidden bg-[url('@/assets/dashboard/imgs/dashboard-background.jpg')] bg-cover bg-center"
        >
          <div class="h-full pl-20px pr-20px">
            <GlobalContent :show-padding="false" />
          </div>
        </div>

        <!-- 页脚 -->
        <DashboardFooter />
      </DashboardContain>
    </div>
  </NConfigProvider>
</template>

<style scoped>
.bg-layout {
  background-color: transparent;
}
</style>
