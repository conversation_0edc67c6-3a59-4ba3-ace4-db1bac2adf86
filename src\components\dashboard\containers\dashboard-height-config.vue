<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { NButton, NCard, NInputNumber, NSpace, NText, NModal, NIcon } from 'naive-ui';

defineOptions({
  name: 'DashboardHeightConfig'
});

interface HeightConfig {
  left: {
    top: number;
    middle: number;
    bottom: number;
  };
  right: {
    top: number;
    middle: number;
    bottom: number;
  };
}

interface Props {
  modelValue: HeightConfig;
  visible?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: HeightConfig): void;
  (e: 'update:visible', value: boolean): void;
  (e: 'apply', value: HeightConfig): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
});

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const emit = defineEmits<Emits>();

const localConfig = ref<HeightConfig>({ ...props.modelValue });

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  localConfig.value = { ...newVal };
}, { deep: true });

// 验证总和是否为100%
const validateSum = (side: 'left' | 'right') => {
  const config = localConfig.value[side];
  return config.top + config.middle + config.bottom === 100;
};

// 自动调整比例
const autoAdjust = (side: 'left' | 'right') => {
  const config = localConfig.value[side];
  const total = config.top + config.middle + config.bottom;

  if (total !== 100) {
    const ratio = 100 / total;
    config.top = Math.round(config.top * ratio);
    config.middle = Math.round(config.middle * ratio);
    config.bottom = 100 - config.top - config.middle; // 确保总和为100
  }
};

// 应用配置
const applyConfig = () => {
  // 自动调整比例
  autoAdjust('left');
  autoAdjust('right');

  emit('update:modelValue', { ...localConfig.value });
  emit('apply', { ...localConfig.value });
  emit('update:visible', false);
};

// 重置为默认值
const resetToDefault = () => {
  localConfig.value = {
    left: { top: 40, middle: 30, bottom: 30 },
    right: { top: 40, middle: 30, bottom: 30 }
  };
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};
</script>

<template>
  <NModal v-model:show="visible" class="flex flex-col items-center justify-center" :mask-closable="false">
    <NCard
      title="卡片高度配置"
      class="w-500px"
      :bordered="false"
      size="small"
      role="dialog"
      aria-modal="true"
    >
      <template #header-extra>
        <NButton text @click="handleClose">
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:close" />
            </NIcon>
          </template>
        </NButton>
      </template>

      <div class="space-y-24px">
        <!-- 左侧列配置 -->
        <div>
          <NText class="text-16px font-600 mb-12px block">左侧列高度配置</NText>
          <NSpace vertical>
            <div class="flex items-center justify-between">
              <NText>数据概览:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.left.top"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <NText>系统监控:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.left.middle"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <NText>实时活动:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.left.bottom"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="text-right">
              <NText :type="validateSum('left') ? 'success' : 'error'" class="text-12px">
                总计: {{ localConfig.left.top + localConfig.left.middle + localConfig.left.bottom }}%
              </NText>
            </div>
          </NSpace>
        </div>

        <!-- 右侧列配置 -->
        <div>
          <NText class="text-16px font-600 mb-12px block">右侧列高度配置</NText>
          <NSpace vertical>
            <div class="flex items-center justify-between">
              <NText>关键指标:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.right.top"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <NText>统计图表:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.right.middle"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <NText>快速操作:</NText>
              <div class="flex items-center gap-8px">
                <NInputNumber
                  v-model:value="localConfig.right.bottom"
                  :min="10"
                  :max="80"
                  :step="5"
                  size="small"
                  class="w-80px"
                />
                <NText>%</NText>
              </div>
            </div>
            <div class="text-right">
              <NText :type="validateSum('right') ? 'success' : 'error'" class="text-12px">
                总计: {{ localConfig.right.top + localConfig.right.middle + localConfig.right.bottom }}%
              </NText>
            </div>
          </NSpace>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <NButton @click="resetToDefault">重置默认</NButton>
          <NSpace>
            <NButton @click="handleClose">取消</NButton>
            <NButton type="primary" @click="applyConfig">应用</NButton>
          </NSpace>
        </div>
      </template>
    </NCard>
  </NModal>
</template>
