<script setup lang="ts">
import { computed, ref } from 'vue';
import { NButton, NIcon } from 'naive-ui';

/**
 * 大屏卡片组件属性
 */
interface Props {
  /** 卡片标题 */
  title: string;
  /** 标题图标 */
  titleIcon?: string;
  /** 卡片类型 */
  cardType?: 'default' | 'chart' | 'metric' | 'info';
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 是否可悬停 */
  hoverable?: boolean;
  /** 动画延迟 */
  animationDelay?: number;
  /** 自定义高度 */
  height?: number | string;
  /** 边框样式 */
  borderStyle?: 'solid' | 'dashed' | 'dotted' | 'glow';
}

interface Emits {
  (e: 'refresh'): void;
  (e: 'fullscreen'): void;
  (e: 'settings'): void;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  titleIcon: 'mdi:view-dashboard',
  height: 100,
  showActions: false,
  hoverable: true,
  animationDelay: 0,
  borderStyle: 'solid'
});

const emit = defineEmits<Emits>();

const isHovered = ref(false);

// 计算卡片样式类
const cardClasses = computed(() => {
  const classes = ['dashboard-card', `card-type-${props.cardType}`, `border-style-${props.borderStyle}`];

  if (props.hoverable) {
    classes.push('hoverable');
  }

  return classes.join(' ');
});

// 计算卡片样式
const cardStyle = computed(() => {
  const style: Record<string, string> = {};

  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }

  if (props.animationDelay) {
    style.animationDelay = `${props.animationDelay}s`;
  }

  return style;
});

// 处理操作按钮点击
const handleRefresh = () => emit('refresh');
const handleFullscreen = () => emit('fullscreen');
const handleSettings = () => emit('settings');
</script>

<template>
  <div
    :class="cardClasses"
    :style="cardStyle"
    class="relative h-full w-full flex flex-col overflow-hidden border border-blue-400/30 rounded-16px bg-white/5 shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-10px transition-all duration-300 ease-in-out"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 卡片头部 -->
    <div
      class="relative h-50px flex items-center justify-between border-b border-blue-400/20 from-blue-600/20 to-blue-400/10 bg-gradient-to-r px-20px"
    >
      <!-- 装饰背景 -->
      <div class="absolute inset-0 opacity-30" />

      <!-- 标题区域 -->
      <div class="relative z-10 flex items-center gap-12px">
        <!-- 标题图标 -->
        <div
          v-if="titleIcon"
          class="h-32px w-32px flex items-center justify-center border border-blue-400/40 rounded-8px bg-blue-500/20"
        >
          <NIcon size="18" class="text-blue-300">
            <SvgIcon :icon="titleIcon" />
          </NIcon>
        </div>

        <!-- 装饰图标 -->
        <div v-else class="relative h-16px w-16px border-2 border-blue-400 rounded-2px">
          <div
            class="absolute left-3px top-3px h-6px w-6px rounded-1px bg-yellow-400 shadow-[0_0_8px_1px_#0091f8,0_0_4px_0_rgba(33,121,195,0.88)]"
          />
        </div>

        <!-- 标题文字 -->
        <h3 class="text-18px text-white font-600 tracking-1px text-shadow-[0_0_4px_#0066ff]">
          {{ title }}
        </h3>

        <!-- 标题额外内容 -->
        <slot name="title-extra" />
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="showActions" class="relative z-10 flex items-center gap-8px">
        <slot name="actions">
          <NButton
            text
            size="small"
            class="text-blue-300 transition-all duration-200 hover:bg-blue-500/20 hover:text-blue-200"
            @click="handleRefresh"
          >
            <template #icon>
              <NIcon size="16">
                <SvgIcon icon="mdi:refresh" />
              </NIcon>
            </template>
          </NButton>

          <NButton
            text
            size="small"
            class="text-blue-300 transition-all duration-200 hover:bg-blue-500/20 hover:text-blue-200"
            @click="handleFullscreen"
          >
            <template #icon>
              <NIcon size="16">
                <SvgIcon icon="mdi:fullscreen" />
              </NIcon>
            </template>
          </NButton>

          <NButton
            text
            size="small"
            class="text-blue-300 transition-all duration-200 hover:bg-blue-500/20 hover:text-blue-200"
            @click="handleSettings"
          >
            <template #icon>
              <NIcon size="16">
                <SvgIcon icon="mdi:cog" />
              </NIcon>
            </template>
          </NButton>
        </slot>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="flex-1 overflow-hidden p-16px">
      <slot />
    </div>

    <!-- 悬停发光效果 -->
    <div
      v-if="hoverable && isHovered"
      class="pointer-events-none absolute inset-0 border border-blue-400/60 rounded-16px shadow-[0_0_20px_rgba(24,144,255,0.4)] transition-all duration-300"
    />

    <!-- 卡片类型装饰 -->
    <div
      v-if="cardType === 'chart'"
      class="absolute right-4px top-4px h-8px w-8px animate-pulse rounded-full bg-green-400"
    />
    <div
      v-if="cardType === 'metric'"
      class="absolute right-4px top-4px h-8px w-8px animate-pulse rounded-full bg-blue-400"
    />
    <div
      v-if="cardType === 'info'"
      class="absolute right-4px top-4px h-8px w-8px animate-pulse rounded-full bg-yellow-400"
    />
  </div>
</template>
