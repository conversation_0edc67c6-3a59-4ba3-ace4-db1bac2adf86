<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  // 限制最小缩放比例，确保在小屏幕上不会过度缩小
  return ww < wh ? ww : wh;
};

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (containerRef.value) {
    if (props.enableScale) {
      containerRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
      containerRef.value.style.overflow = 'hidden';
    } else {
      containerRef.value.style.transform = 'translate(-50%, -50%)';
      containerRef.value.style.overflow = 'auto';
    }
  }
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  if (containerRef.value) {
    if (props.enableScale) {
      containerRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
      containerRef.value.style.overflow = 'hidden';
    } else {
      containerRef.value.style.transform = 'translate(-50%, -50%)';
      containerRef.value.style.overflow = 'auto';
    }
    containerRef.value.style.width = `${props.designWidth}px`;
    containerRef.value.style.height = `${props.designHeight}px`;
  }
  window.addEventListener('resize', resize);
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    resize();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize);
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761]">
    <!-- 主容器 -->
    <div
      ref="containerRef"
      class="fixed left-1/2 top-1/2 z-999 flex flex-col origin-top-left transition-all duration-300 dashboard-container"
    >
      <slot />
    </div>
  </div>
</template>

<style scoped>
/* 大屏滚动条样式 */
.dashboard-container {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(24, 144, 255, 0.6) rgba(255, 255, 255, 0.1);
}

.dashboard-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.dashboard-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(24, 144, 255, 0.8) 0%, rgba(24, 144, 255, 0.4) 100%);
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(24, 144, 255, 1) 0%, rgba(24, 144, 255, 0.6) 100%);
}

.dashboard-container::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}
</style>
