<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  // 限制最小缩放比例，确保在小屏幕上不会过度缩小
  return ww < wh ? ww : wh;
};

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (!containerRef.value) return;

  // 使用 requestAnimationFrame 优化性能，并添加防抖
  requestAnimationFrame(() => {
    if (!containerRef.value) return;

    // 添加过渡类名以实现流畅动画
    containerRef.value.classList.add('dashboard-transitioning');

    if (props.enableScale) {
      // 缩放模式：固定尺寸，居中缩放
      const scale = getScale();
      containerRef.value.style.cssText = `
        transform: scale(${scale}) translate(-50%, -50%);
        transform-origin: top left;
        overflow: hidden;
        width: ${props.designWidth}px;
        height: ${props.designHeight}px;
        position: fixed;
        left: 50%;
        top: 50%;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform;
      `;
    } else {
      // 非缩放模式：全屏显示，主容器不滚动
      containerRef.value.style.cssText = `
        transform: none;
        transform-origin: top left;
        overflow: hidden;
        width: 100%;
        height: 100%;
        position: relative;
        left: 0;
        top: 0;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: transform;
      `;
    }

    // 动画完成后移除过渡类名
    setTimeout(() => {
      if (containerRef.value) {
        containerRef.value.classList.remove('dashboard-transitioning');
      }
    }, 600);
  });
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  // 初始化时直接调用 resize 方法
  resize();
  window.addEventListener('resize', resize);
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    resize();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize);
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761]">
    <!-- 主容器 -->
    <div ref="containerRef" class="dashboard-container z-999 flex flex-col origin-top-left transition-all duration-300">
      <slot />
    </div>
  </div>
</template>

<style scoped>
/* 大屏容器基础样式 */
.dashboard-container {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(24, 144, 255, 0.6) rgba(255, 255, 255, 0.1);

  /* 硬件加速优化 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 过渡动画状态 */
.dashboard-container.dashboard-transitioning {
  /* 在过渡期间禁用指针事件，避免卡顿 */
  pointer-events: none;
}

.dashboard-container.dashboard-transitioning * {
  /* 子元素也禁用指针事件 */
  pointer-events: none;
}

/* 滚动条样式 */
.dashboard-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.dashboard-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(24, 144, 255, 0.8) 0%, rgba(24, 144, 255, 0.4) 100%);
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 0.3);
  box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(24, 144, 255, 1) 0%, rgba(24, 144, 255, 0.6) 100%);
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
}

.dashboard-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(24, 144, 255, 1) 0%, rgba(24, 144, 255, 0.8) 100%);
}

.dashboard-container::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* 缩放动画优化 */
@media (prefers-reduced-motion: no-preference) {
  .dashboard-container {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .dashboard-container {
    transition: none;
  }
}
</style>
