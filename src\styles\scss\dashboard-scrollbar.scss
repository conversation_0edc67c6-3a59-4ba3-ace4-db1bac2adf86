// 大屏滚动条样式
.dashboard-scrollbar {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: rgba(24, 144, 255, 0.6) rgba(255, 255, 255, 0.1);

  /* Webkit */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(24, 144, 255, 0.8) 0%, rgba(24, 144, 255, 0.4) 100%);
    border-radius: 4px;
    border: 1px solid rgba(24, 144, 255, 0.3);
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(24, 144, 255, 1) 0%, rgba(24, 144, 255, 0.6) 100%);
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
  }

  &::-webkit-scrollbar-thumb:active {
    background: linear-gradient(180deg, rgba(24, 144, 255, 1) 0%, rgba(24, 144, 255, 0.8) 100%);
  }

  &::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 应用到大屏容器
.dashboard-container {
  @extend .dashboard-scrollbar;
}

// 应用到卡片内容区域
.dashboard-card .card-content {
  @extend .dashboard-scrollbar;
}

// 全局大屏滚动条样式（当关闭缩放时）
.dashboard-layout {
  .dashboard-scrollbar,
  &.no-scale {
    @extend .dashboard-scrollbar;
  }
}

// 细滚动条变体（用于小区域）
.dashboard-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(24, 144, 255, 0.5) rgba(255, 255, 255, 0.05);

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(24, 144, 255, 0.5);
    border-radius: 3px;
    border: none;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(24, 144, 255, 0.7);
  }
}
