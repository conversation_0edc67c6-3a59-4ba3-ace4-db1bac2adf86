<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { NButton, NIcon, NInput, NModal } from 'naive-ui';

interface Props {
  visible: boolean;
}

interface ChatMessage {
  role: 'user' | 'system';
  content: string;
  time: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);
const chatContainer = ref<HTMLElement>();

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 发送消息
const message = ref('');
const isTyping = ref(false);
const chatMessages = ref<ChatMessage[]>([
  {
    role: 'system',
    content:
      '您好！我是智能数据大屏助手，可以帮助您：\n\n• 解答大屏功能问题\n• 提供数据分析建议\n• 协助系统操作指导\n\n请问有什么可以帮助您的？',
    time: new Date().toLocaleTimeString()
  }
]);

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    }
  });
};

const sendMessage = () => {
  if (!message.value.trim() || isTyping.value) return;

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: message.value,
    time: new Date().toLocaleTimeString()
  });

  message.value = '';
  scrollToBottom();

  // 模拟AI回复
  isTyping.value = true;
  setTimeout(
    () => {
      const responses = [
        '感谢您的问题！根据当前大屏数据，我建议您关注以下几个关键指标...',
        '这是一个很好的问题。从数据分析角度来看，您可以尝试以下操作...',
        '我理解您的需求。让我为您提供详细的解决方案...',
        '基于您的询问，我推荐您查看系统监控模块，那里有更详细的信息。'
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];

      chatMessages.value.push({
        role: 'system',
        content: randomResponse,
        time: new Date().toLocaleTimeString()
      });

      isTyping.value = false;
      scrollToBottom();
    },
    1000 + Math.random() * 1000
  );
};
</script>

<template>
  <NModal v-model:show="dialogVisible" class="flex flex-col items-center justify-center" :mask-closable="false">
    <div
      class="w-600px overflow-hidden border border-blue-400/30 rounded-16px bg-gray-900/95 shadow-[0_20px_60px_rgba(0,0,0,0.5)] backdrop-blur-20px"
    >
      <!-- 头部 -->
      <div
        class="flex items-center justify-between border-b border-blue-400/20 from-blue-600/20 to-blue-500/10 bg-gradient-to-r p-20px"
      >
        <div class="flex items-center gap-12px">
          <div
            class="h-40px w-40px flex items-center justify-center border border-blue-400/40 rounded-12px bg-blue-500/20"
          >
            <NIcon size="20" class="text-blue-300">
              <SvgIcon icon="mdi:robot" />
            </NIcon>
          </div>
          <div>
            <h2 class="text-18px text-white font-600">智能助手</h2>
            <p class="text-12px text-blue-300">AI数据大屏助手</p>
          </div>
        </div>

        <NButton
          quaternary
          circle
          class="text-gray-400 transition-all duration-200 hover:bg-gray-700/50 hover:text-white"
          @click="closeDialog"
        >
          <template #icon>
            <NIcon size="18">
              <SvgIcon icon="mdi:close" />
            </NIcon>
          </template>
        </NButton>
      </div>

      <!-- 对话内容区域 -->
      <div
        ref="chatContainer"
        class="h-400px overflow-y-auto from-gray-800/50 to-gray-900/50 bg-gradient-to-b p-16px space-y-16px"
      >
        <div
          v-for="(msg, index) in chatMessages"
          :key="index"
          class="flex gap-12px"
          :class="msg.role === 'user' ? 'flex-row-reverse' : ''"
        >
          <!-- 头像 -->
          <div
            class="h-36px w-36px flex flex-shrink-0 items-center justify-center border rounded-12px"
            :class="msg.role === 'user' ? 'bg-blue-500/20 border-blue-400/40' : 'bg-green-500/20 border-green-400/40'"
          >
            <NIcon size="18" :class="msg.role === 'user' ? 'text-blue-300' : 'text-green-300'">
              <SvgIcon :icon="msg.role === 'user' ? 'mdi:account' : 'mdi:robot'" />
            </NIcon>
          </div>

          <!-- 消息内容 -->
          <div class="max-w-[80%] flex-1">
            <div
              class="border rounded-12px p-12px backdrop-blur-10px"
              :class="
                msg.role === 'user'
                  ? 'bg-blue-500/10 border-blue-400/30 text-blue-100'
                  : 'bg-gray-700/50 border-gray-600/50 text-gray-100'
              "
            >
              <div class="whitespace-pre-line text-14px leading-relaxed">{{ msg.content }}</div>
              <div class="mt-8px text-right text-11px opacity-60">
                {{ msg.time }}
              </div>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="flex gap-12px">
          <div
            class="h-36px w-36px flex flex-shrink-0 items-center justify-center border border-green-400/40 rounded-12px bg-green-500/20"
          >
            <NIcon size="18" class="text-green-300">
              <SvgIcon icon="mdi:robot" />
            </NIcon>
          </div>
          <div class="flex-1">
            <div class="border border-gray-600/50 rounded-12px bg-gray-700/50 p-12px backdrop-blur-10px">
              <div class="flex items-center gap-8px text-gray-300">
                <div class="flex gap-2px">
                  <div class="h-6px w-6px animate-bounce rounded-full bg-green-400" style="animation-delay: 0ms" />
                  <div class="h-6px w-6px animate-bounce rounded-full bg-green-400" style="animation-delay: 150ms" />
                  <div class="h-6px w-6px animate-bounce rounded-full bg-green-400" style="animation-delay: 300ms" />
                </div>
                <span class="text-12px">AI正在思考中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="border-t border-gray-700/50 bg-gray-800/50 p-16px">
        <div class="flex gap-12px">
          <NInput
            v-model:value="message"
            type="text"
            placeholder="请输入您的问题..."
            class="flex-1"
            :disabled="isTyping"
            @keyup.enter="sendMessage"
          >
            <template #prefix>
              <NIcon class="text-blue-400">
                <SvgIcon icon="mdi:message-text" />
              </NIcon>
            </template>
          </NInput>

          <NButton
            type="primary"
            :disabled="!message.trim() || isTyping"
            :loading="isTyping"
            class="bg-blue-600 px-20px hover:bg-blue-500"
            @click="sendMessage"
          >
            <template #icon>
              <NIcon>
                <SvgIcon icon="mdi:send" />
              </NIcon>
            </template>
            发送
          </NButton>
        </div>

        <!-- 提示信息 -->
        <div class="mt-12px text-center text-11px text-gray-400">
          按 Enter 发送消息 • AI助手可以帮助您解答大屏相关问题
        </div>
      </div>
    </div>
  </NModal>
</template>
